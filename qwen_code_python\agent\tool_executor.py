"""工具执行器"""
import importlib
from typing import Dict, Any, List
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)

class ToolExecutor:
    """工具执行器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.tools = {}
        self._load_tools()
    
    def _load_tools(self):
        """加载工具"""
        enabled_tools = self.config.enabled_tools

        # 工具名称到类名的映射
        tool_class_mapping = {
            "edit_file": "EditFileTool",
            "read_file": "ReadFileTool",
            "write_file": "WriteFileTool",
            "shell_command": "ShellCommandTool",
            "ls": "LsTool",
            "grep": "GrepTool",
            "save_memory": "SaveMemoryTool",
            "load_memory": "LoadMemoryTool",
            "read_many_files": "ReadManyFilesTool",
            "str_replace_edit": "StrReplaceEditTool"
        }

        for tool_name in enabled_tools:
            try:
                # 动态导入工具模块
                module_name = f"..tools.{tool_name}"
                module = importlib.import_module(module_name, package=__name__)

                # 获取工具类
                class_name = tool_class_mapping.get(tool_name)
                if not class_name:
                    # 回退到驼峰命名
                    class_name = ''.join(word.capitalize() for word in tool_name.split('_')) + 'Tool'

                tool_class = getattr(module, class_name)

                # 实例化工具
                tool_instance = tool_class(self.config)
                self.tools[tool_name] = tool_instance

                logger.info(f"Loaded tool: {tool_name}")

            except Exception as e:
                logger.warning(f"Failed to load tool {tool_name}: {e}")
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """获取工具定义"""
        definitions = []
        
        for tool_name, tool in self.tools.items():
            definitions.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            })
        
        return definitions
    
    async def execute_tool(self, tool_name: str, args: Dict[str, Any]):
        """执行工具"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool not found: {tool_name}")
        
        tool = self.tools[tool_name]
        return await tool.execute(args)