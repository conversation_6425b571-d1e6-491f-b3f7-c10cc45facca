"""日志系统"""
import logging
import sys
from typing import Optional

def setup_logger(debug: bool = False):
    """设置日志系统"""
    level = logging.DEBUG if debug else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)