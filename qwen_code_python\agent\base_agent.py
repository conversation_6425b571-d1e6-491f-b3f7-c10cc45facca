"""基础 Agent 类"""
from abc import ABC, abstractmethod
from typing import AsyncGenerator, Dict, Any, Optional

from ..utils.config import Config

class BaseAgent(ABC):
    """Agent 基类"""
    
    def __init__(self, config: Config):
        self.config = config
    
    @abstractmethod
    async def send_message(self, message: str, context: Optional[Dict] = None) -> AsyncGenerator:
        """发送消息的抽象方法"""
        pass