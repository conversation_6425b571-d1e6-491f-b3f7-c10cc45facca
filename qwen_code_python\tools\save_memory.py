"""内存保存工具"""
import os
import json
from typing import Dict, Any
from datetime import datetime
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class SaveMemoryTool(BaseTool):
    """内存保存工具"""
    
    name = "save_memory"
    description = "Save important information to memory for future reference"
    
    parameters = {
        "type": "object",
        "properties": {
            "key": {
                "type": "string",
                "description": "Unique key to identify this memory item"
            },
            "content": {
                "type": "string",
                "description": "Content to save to memory"
            },
            "category": {
                "type": "string",
                "description": "Category for organizing memories (optional)",
                "default": "general"
            },
            "tags": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Tags for easier searching (optional)",
                "default": []
            }
        },
        "required": ["key", "content"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行内存保存"""
        try:
            key = args["key"]
            content = args["content"]
            category = args.get("category", "general")
            tags = args.get("tags", [])
            
            # 确保内存目录存在
            memory_dir = self.config.memory_dir
            os.makedirs(memory_dir, exist_ok=True)
            
            # 内存文件路径
            memory_file = os.path.join(memory_dir, "memories.json")
            
            # 加载现有内存
            memories = {}
            if os.path.exists(memory_file):
                try:
                    async with aiofiles.open(memory_file, 'r', encoding='utf-8') as f:
                        content_str = await f.read()
                        if content_str.strip():
                            memories = json.loads(content_str)
                except Exception as e:
                    logger.warning(f"Error loading existing memories: {e}")
                    memories = {}
            
            # 创建内存条目
            memory_entry = {
                "content": content,
                "category": category,
                "tags": tags,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # 检查是否是更新现有内存
            is_update = key in memories
            if is_update:
                # 保留创建时间，更新修改时间
                memory_entry["created_at"] = memories[key].get("created_at", memory_entry["created_at"])
            
            # 保存内存
            memories[key] = memory_entry
            
            # 写入文件
            async with aiofiles.open(memory_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(memories, indent=2, ensure_ascii=False))
            
            # 更新索引文件（用于快速搜索）
            await self._update_memory_index(memory_dir, memories)
            
            action = "Updated" if is_update else "Saved"
            
            return ToolResult(
                success=True,
                content=f"{action} memory: {key}",
                display=f"💾 {action} memory: {key}\nCategory: {category}\nTags: {', '.join(tags) if tags else 'None'}"
            )
            
        except Exception as e:
            logger.error(f"Error in save_memory: {e}")
            return ToolResult(
                success=False,
                content=f"Error saving memory: {str(e)}",
                display=f"❌ Error saving memory: {str(e)}"
            )
    
    async def _update_memory_index(self, memory_dir: str, memories: Dict[str, Any]):
        """更新内存索引文件"""
        try:
            index_file = os.path.join(memory_dir, "memory_index.json")
            
            # 创建索引
            index = {
                "total_memories": len(memories),
                "categories": {},
                "tags": {},
                "last_updated": datetime.now().isoformat()
            }
            
            # 统计分类和标签
            for key, memory in memories.items():
                category = memory.get("category", "general")
                tags = memory.get("tags", [])
                
                # 分类统计
                if category not in index["categories"]:
                    index["categories"][category] = []
                index["categories"][category].append(key)
                
                # 标签统计
                for tag in tags:
                    if tag not in index["tags"]:
                        index["tags"][tag] = []
                    index["tags"][tag].append(key)
            
            # 写入索引文件
            async with aiofiles.open(index_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(index, indent=2, ensure_ascii=False))
                
        except Exception as e:
            logger.warning(f"Error updating memory index: {e}")


class LoadMemoryTool(BaseTool):
    """内存加载工具"""
    
    name = "load_memory"
    description = "Load and search saved memories"
    
    parameters = {
        "type": "object",
        "properties": {
            "key": {
                "type": "string",
                "description": "Specific memory key to load (optional)"
            },
            "category": {
                "type": "string",
                "description": "Filter by category (optional)"
            },
            "tag": {
                "type": "string",
                "description": "Filter by tag (optional)"
            },
            "search": {
                "type": "string",
                "description": "Search in memory content (optional)"
            },
            "limit": {
                "type": "integer",
                "description": "Maximum number of results to return",
                "default": 10
            }
        },
        "required": []
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行内存加载"""
        try:
            key = args.get("key")
            category = args.get("category")
            tag = args.get("tag")
            search = args.get("search")
            limit = args.get("limit", 10)
            
            # 内存文件路径
            memory_dir = self.config.memory_dir
            memory_file = os.path.join(memory_dir, "memories.json")
            
            if not os.path.exists(memory_file):
                return ToolResult(
                    success=True,
                    content="No memories found.",
                    display="💾 No memories saved yet."
                )
            
            # 加载内存
            async with aiofiles.open(memory_file, 'r', encoding='utf-8') as f:
                content_str = await f.read()
                if not content_str.strip():
                    return ToolResult(
                        success=True,
                        content="No memories found.",
                        display="💾 No memories saved yet."
                    )
                
                memories = json.loads(content_str)
            
            # 如果指定了具体的key
            if key:
                if key in memories:
                    memory = memories[key]
                    result = f"Key: {key}\n"
                    result += f"Category: {memory.get('category', 'general')}\n"
                    result += f"Tags: {', '.join(memory.get('tags', []))}\n"
                    result += f"Created: {memory.get('created_at', 'Unknown')}\n"
                    result += f"Content:\n{memory['content']}"
                    
                    return ToolResult(
                        success=True,
                        content=result,
                        display=f"💾 Memory: {key}\n{result}"
                    )
                else:
                    return ToolResult(
                        success=False,
                        content=f"Memory not found: {key}",
                        display=f"❌ Memory not found: {key}"
                    )
            
            # 过滤内存
            filtered_memories = self._filter_memories(memories, category, tag, search)
            
            if not filtered_memories:
                return ToolResult(
                    success=True,
                    content="No matching memories found.",
                    display="💾 No matching memories found."
                )
            
            # 限制结果数量
            if len(filtered_memories) > limit:
                filtered_memories = dict(list(filtered_memories.items())[:limit])
            
            # 格式化结果
            result_lines = []
            for mem_key, memory in filtered_memories.items():
                result_lines.append(f"=== {mem_key} ===")
                result_lines.append(f"Category: {memory.get('category', 'general')}")
                result_lines.append(f"Tags: {', '.join(memory.get('tags', []))}")
                result_lines.append(f"Created: {memory.get('created_at', 'Unknown')}")
                result_lines.append(f"Content: {memory['content'][:200]}{'...' if len(memory['content']) > 200 else ''}")
                result_lines.append("")
            
            result_content = "\n".join(result_lines)
            
            return ToolResult(
                success=True,
                content=result_content,
                display=f"💾 Found {len(filtered_memories)} memories\n{result_content}"
            )
            
        except Exception as e:
            logger.error(f"Error in load_memory: {e}")
            return ToolResult(
                success=False,
                content=f"Error loading memory: {str(e)}",
                display=f"❌ Error loading memory: {str(e)}"
            )
    
    def _filter_memories(self, memories: Dict[str, Any], category: str = None, tag: str = None, search: str = None) -> Dict[str, Any]:
        """过滤内存"""
        filtered = {}
        
        for key, memory in memories.items():
            # 分类过滤
            if category and memory.get("category", "general") != category:
                continue
            
            # 标签过滤
            if tag and tag not in memory.get("tags", []):
                continue
            
            # 内容搜索
            if search:
                search_lower = search.lower()
                if (search_lower not in key.lower() and 
                    search_lower not in memory["content"].lower() and
                    search_lower not in memory.get("category", "").lower() and
                    not any(search_lower in t.lower() for t in memory.get("tags", []))):
                    continue
            
            filtered[key] = memory
        
        return filtered
