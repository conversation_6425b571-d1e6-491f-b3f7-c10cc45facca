"""文件写入工具"""
import os
import difflib
from typing import Dict, Any
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class WriteFileTool(BaseTool):
    """文件写入工具"""
    
    name = "write_file"
    description = "Write content to a file. Creates new file or overwrites existing file."
    
    parameters = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to write (relative or absolute)"
            },
            "content": {
                "type": "string",
                "description": "Content to write to the file"
            },
            "create_dirs": {
                "type": "boolean",
                "description": "Whether to create parent directories if they don't exist (default: true)",
                "default": True
            }
        },
        "required": ["file_path", "content"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行文件写入"""
        try:
            file_path = args["file_path"]
            content = args["content"]
            create_dirs = args.get("create_dirs", True)
            
            # 检查是否需要创建目录
            dir_path = os.path.dirname(file_path)
            if dir_path and not os.path.exists(dir_path):
                if create_dirs:
                    os.makedirs(dir_path, exist_ok=True)
                else:
                    return ToolResult(
                        success=False,
                        content=f"Directory does not exist: {dir_path}",
                        display=f"❌ Directory does not exist: {dir_path}"
                    )
            
            # 检查文件是否已存在
            file_exists = os.path.exists(file_path)
            original_content = ""
            
            if file_exists:
                # 读取原文件内容用于比较
                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        original_content = await f.read()
                except UnicodeDecodeError:
                    # 如果原文件不是文本文件，直接覆盖
                    original_content = "[Binary file - cannot display diff]"
            
            # 写入文件
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            # 生成结果信息
            lines_count = len(content.split('\n'))
            chars_count = len(content)
            
            if file_exists:
                # 生成差异信息
                if original_content != "[Binary file - cannot display diff]":
                    diff = self._generate_diff(original_content, content, file_path)
                    action = "Overwrote"
                else:
                    diff = "Binary file overwritten with text content."
                    action = "Overwrote"
            else:
                diff = f"New file created with {lines_count} lines."
                action = "Created"
            
            display_info = f"✅ {action} {file_path}\n"
            display_info += f"Lines: {lines_count}, Characters: {chars_count}\n\n{diff}"
            
            return ToolResult(
                success=True,
                content=f"Successfully wrote to {file_path}",
                display=display_info
            )
            
        except Exception as e:
            logger.error(f"Error in write_file: {e}")
            return ToolResult(
                success=False,
                content=f"Error writing file: {str(e)}",
                display=f"❌ Error writing file: {str(e)}"
            )
    
    def _generate_diff(self, original: str, new: str, filename: str) -> str:
        """生成差异显示"""
        if original == new:
            return "No changes made."
        
        original_lines = original.splitlines(keepends=True)
        new_lines = new.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            new_lines,
            fromfile=f"{filename} (original)",
            tofile=f"{filename} (new)",
            lineterm=""
        )
        
        diff_text = "".join(diff)
        if len(diff_text) > 1500:
            return f"Large diff ({len(diff_text)} chars) - file successfully written."
        
        return diff_text if diff_text else "File content updated."
