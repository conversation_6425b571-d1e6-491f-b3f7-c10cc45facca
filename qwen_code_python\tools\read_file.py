"""文件读取工具"""
import os
import mimetypes
from typing import Dict, Any, Optional
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ReadFileTool(BaseTool):
    """文件读取工具"""
    
    name = "read_file"
    description = "Read and return the content of a specified file. Supports text files with optional line range."
    
    parameters = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to read (relative or absolute)"
            },
            "start_line": {
                "type": "integer",
                "description": "Optional: Starting line number (1-based) for partial read"
            },
            "end_line": {
                "type": "integer", 
                "description": "Optional: Ending line number (1-based) for partial read"
            }
        },
        "required": ["file_path"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行文件读取"""
        try:
            file_path = args["file_path"]
            start_line = args.get("start_line")
            end_line = args.get("end_line")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    content=f"File not found: {file_path}",
                    display=f"❌ File not found: {file_path}"
                )
            
            # 检查是否是目录
            if os.path.isdir(file_path):
                return ToolResult(
                    success=False,
                    content=f"Path is a directory, not a file: {file_path}",
                    display=f"❌ Path is a directory: {file_path}"
                )
            
            # 检查文件类型
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type and not mime_type.startswith('text/'):
                # 对于非文本文件，返回基本信息
                file_size = os.path.getsize(file_path)
                return ToolResult(
                    success=True,
                    content=f"Binary file: {file_path} (size: {file_size} bytes, type: {mime_type})",
                    display=f"📄 Binary file: {file_path} ({file_size} bytes)"
                )
            
            # 读取文件内容
            try:
                async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    async with aiofiles.open(file_path, 'r', encoding='gbk') as f:
                        content = await f.read()
                except UnicodeDecodeError:
                    file_size = os.path.getsize(file_path)
                    return ToolResult(
                        success=False,
                        content=f"Cannot decode file as text: {file_path}",
                        display=f"❌ Cannot decode file as text: {file_path} ({file_size} bytes)"
                    )
            
            # 处理行范围读取
            if start_line is not None or end_line is not None:
                lines = content.split('\n')
                total_lines = len(lines)
                
                # 验证行号
                if start_line is not None and (start_line < 1 or start_line > total_lines):
                    return ToolResult(
                        success=False,
                        content=f"Invalid start_line: {start_line}. File has {total_lines} lines.",
                        display=f"❌ Invalid start_line: {start_line}"
                    )
                
                if end_line is not None and (end_line < 1 or end_line > total_lines):
                    return ToolResult(
                        success=False,
                        content=f"Invalid end_line: {end_line}. File has {total_lines} lines.",
                        display=f"❌ Invalid end_line: {end_line}"
                    )
                
                # 设置默认值
                start_idx = (start_line - 1) if start_line is not None else 0
                end_idx = end_line if end_line is not None else total_lines
                
                if start_line is not None and end_line is not None and start_line > end_line:
                    return ToolResult(
                        success=False,
                        content=f"start_line ({start_line}) cannot be greater than end_line ({end_line})",
                        display=f"❌ Invalid line range"
                    )
                
                # 提取指定行
                selected_lines = lines[start_idx:end_idx]
                content = '\n'.join(selected_lines)
                
                range_info = f" (lines {start_line or 1}-{end_line or total_lines})"
            else:
                range_info = ""
            
            # 文件统计信息
            lines_count = len(content.split('\n'))
            chars_count = len(content)
            file_size = os.path.getsize(file_path)
            
            display_info = f"📄 Read {file_path}{range_info}\n"
            display_info += f"Lines: {lines_count}, Characters: {chars_count}, Size: {file_size} bytes"
            
            return ToolResult(
                success=True,
                content=content,
                display=display_info
            )
            
        except Exception as e:
            logger.error(f"Error in read_file: {e}")
            return ToolResult(
                success=False,
                content=f"Error reading file: {str(e)}",
                display=f"❌ Error reading file: {str(e)}"
            )
