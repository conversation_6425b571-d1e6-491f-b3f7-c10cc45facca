"""Qwen Code Agent - 主要的 Agent 实现"""
import asyncio
from typing import AsyncGenerator, List, Dict, Any, Optional
from dataclasses import dataclass

from .base_agent import BaseAgent
from .turn import Turn
from .tool_executor import ToolExecutor
from ..llm.llm_client import LL<PERSON>lient
from ..memory.memory_manager import MemoryManager
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)

@dataclass
class AgentEvent:
    type: str
    data: Any
    timestamp: float

class QwenAgent(BaseAgent):
    """Qwen Code 主 Agent 类"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.llm_client: LLMClient = self._create_llm_client()
        self.tool_executor = ToolExecutor(config)
        self.memory_manager = MemoryManager(config)
        self.current_turn: Optional[Turn] = None
        
    def _create_llm_client(self) -> LLMClient:
        """根据配置创建 LLM 客户端"""
        if self.config.llm_provider == "qwen":
            from ..llm.qwen_client import QwenClient
            return QwenClient(self.config)
        elif self.config.llm_provider == "openai":
            from ..llm.openai_client import OpenAIClient
            return OpenAIClient(self.config)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.config.llm_provider}")
    
    async def send_message(self, message: str, context: Optional[Dict] = None) -> AsyncGenerator[AgentEvent, None]:
        """发送消息并返回流式响应"""
        try:
            # 1. 刷新内存和上下文
            await self.memory_manager.refresh_memory()
            
            # 2. 创建新的 Turn
            self.current_turn = Turn(
                agent=self,
                llm_client=self.llm_client,
                tool_executor=self.tool_executor,
                memory_manager=self.memory_manager
            )
            
            # 3. 执行 agentic 循环
            async for event in self.current_turn.run(message, context):
                yield event
                
        except Exception as e:
            logger.error(f"Error in send_message: {e}")
            yield AgentEvent(type="error", data=str(e), timestamp=asyncio.get_event_loop().time())
    
    async def compress_chat_if_needed(self) -> bool:
        """如果需要则压缩聊天历史"""
        return await self.memory_manager.compress_chat_if_needed()
