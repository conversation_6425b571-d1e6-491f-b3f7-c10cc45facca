"""Turn - 单轮对话执行逻辑"""
import asyncio
import json
from typing import AsyncGenerator, Dict, Any, Optional, List
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ToolCallRequest:
    name: str
    args: Dict[str, Any]
    call_id: str

class Turn:
    """单轮对话的执行逻辑"""
    
    def __init__(self, agent, llm_client, tool_executor, memory_manager):
        self.agent = agent
        self.llm_client = llm_client
        self.tool_executor = tool_executor
        self.memory_manager = memory_manager
        self.pending_tool_calls: List[ToolCallRequest] = []
        
    async def run(self, message: str, context: Optional[Dict] = None) -> AsyncGenerator:
        """执行单轮对话"""
        try:
            # 1. 构建完整的提示词
            full_prompt = await self._build_prompt(message, context)

            # 2. 获取工具定义
            tools = self.tool_executor.get_tool_definitions()

            # 3. 发送到 LLM 并处理响应
            async for response in self.llm_client.generate_stream(full_prompt, tools=tools):
                if response.type == "text":
                    yield {"type": "text", "data": response.content, "timestamp": asyncio.get_event_loop().time()}
                elif response.type == "tool_call":
                    # 处理工具调用
                    tool_result = await self._execute_tool_call(response.tool_call)
                    yield {"type": "tool_result", "data": tool_result, "timestamp": asyncio.get_event_loop().time()}

                    # 如果工具调用成功，可以继续对话（简化版本，不递归调用）
                    if tool_result.get("success"):
                        yield {"type": "text", "data": f"\n✅ Tool executed successfully: {tool_result.get('tool_name')}", "timestamp": asyncio.get_event_loop().time()}
                elif response.type == "error":
                    yield {"type": "error", "data": response.content, "timestamp": asyncio.get_event_loop().time()}

        except Exception as e:
            logger.error(f"Error in turn execution: {e}")
            yield {"type": "error", "data": str(e), "timestamp": asyncio.get_event_loop().time()}
    
    async def _build_prompt(self, message: str, context: Optional[Dict] = None) -> str:
        """构建完整的提示词"""
        # 获取内存内容
        memory_content = await self.memory_manager.get_memory_content()
        
        # 构建基础提示词
        prompt_parts = []
        
        # 系统提示词
        system_prompt = """You are Qwen Code, an AI coding assistant. You help users understand, edit, and work with codebases.

You have access to tools that allow you to:
- Read and edit files
- Execute shell commands
- Save important information to memory

When working with code:
1. Always read relevant files first to understand the context
2. Make precise, targeted changes
3. Test your changes when possible
4. Save important insights to memory for future reference

Be helpful, accurate, and efficient in your responses."""
        
        prompt_parts.append(system_prompt)
        
        # 添加内存内容
        if memory_content:
            prompt_parts.append(f"# Memory\n{memory_content}")
        
        # 添加用户消息
        prompt_parts.append(f"# User Request\n{message}")
        
        return "\n\n".join(prompt_parts)
    
    def _build_tool_result_prompt(self, tool_result: Dict[str, Any]) -> str:
        """构建工具结果的提示词"""
        return f"Tool execution result: {json.dumps(tool_result, indent=2)}\n\nPlease continue based on this result."
    
    async def _execute_tool_call(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具调用"""
        try:
            tool_name = tool_call.get("name")
            tool_args = json.loads(tool_call.get("args", "{}"))
            
            result = await self.tool_executor.execute_tool(tool_name, tool_args)
            
            return {
                "success": True,
                "result": result.content if hasattr(result, 'content') else str(result),
                "tool_name": tool_name,
                "display": result.display if hasattr(result, 'display') else str(result)
            }
        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_call.get("name", "unknown")
            }
