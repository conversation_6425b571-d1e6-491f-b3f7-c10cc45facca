"""内存管理系统"""
import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..utils.config import Config
from ..utils.logger import get_logger
from ..utils.file_utils import find_qwen_files

logger = get_logger(__name__)

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.working_dir = config.working_dir
        self.memory_content = ""
        self.chat_history: List[Dict[str, Any]] = []
        
    async def refresh_memory(self) -> Dict[str, Any]:
        """刷新内存内容"""
        try:
            # 1. 加载 QWEN.md 文件
            qwen_files = find_qwen_files(self.working_dir)
            memory_parts = []
            
            for qwen_file in qwen_files:
                try:
                    with open(qwen_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        memory_parts.append(f"# {qwen_file}\n{content}")
                except Exception as e:
                    logger.warning(f"Failed to read {qwen_file}: {e}")
            
            # 2. 合并内存内容
            self.memory_content = "\n\n".join(memory_parts)
            
            return {
                "memory_content": self.memory_content,
                "file_count": len(qwen_files)
            }
            
        except Exception as e:
            logger.error(f"Failed to refresh memory: {e}")
            return {"memory_content": "", "file_count": 0}
    
    async def get_memory_content(self) -> str:
        """获取内存内容"""
        return self.memory_content
    
    async def save_memory(self, fact: str) -> bool:
        """保存记忆到文件"""
        try:
            qwen_file = os.path.join(self.working_dir, "QWEN.md")
            
            # 读取现有内容
            existing_content = ""
            if os.path.exists(qwen_file):
                with open(qwen_file, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
            
            # 添加新的记忆
            memory_section = "\n## Qwen Added Memories\n"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            new_memory = f"\n- [{timestamp}] {fact}\n"
            
            if "## Qwen Added Memories" in existing_content:
                # 在现有记忆部分添加
                updated_content = existing_content + new_memory
            else:
                # 创建新的记忆部分
                updated_content = existing_content + memory_section + new_memory
            
            # 写入文件
            with open(qwen_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            # 刷新内存
            await self.refresh_memory()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save memory: {e}")
            return False
    
    async def compress_chat_if_needed(self) -> bool:
        """如果需要则压缩聊天历史"""
        # 简单的压缩策略：如果历史超过一定长度，保留最近的对话
        max_history_length = self.config.max_chat_history_length
        
        if len(self.chat_history) > max_history_length:
            # 保留最近的对话
            self.chat_history = self.chat_history[-max_history_length//2:]
            logger.info(f"Compressed chat history to {len(self.chat_history)} messages")
            return True
        
        return False
    
    def add_to_chat_history(self, role: str, content: str, metadata: Optional[Dict] = None):
        """添加到聊天历史"""
        self.chat_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        })