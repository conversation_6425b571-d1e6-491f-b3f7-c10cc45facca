"""Qwen LLM 客户端实现"""
import asyncio
from typing import Async<PERSON>enerator, Dict, Any, Optional
import openai
from dataclasses import dataclass

from .llm_client import LLMClient, LLMResponse
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class QwenResponse(LLMResponse):
    """Qwen 响应格式"""
    pass

class QwenClient(LLMClient):
    """Qwen 模型客户端"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.client = openai.AsyncOpenAI(
            api_key=config.qwen_api_key,
            base_url=config.qwen_base_url
        )
        self.model = config.qwen_model
    
    async def generate_stream(self, prompt: str, **kwargs) -> AsyncGenerator[LLMResponse, None]:
        """流式生成响应"""
        try:
            messages = [{"role": "user", "content": prompt}]

            # 添加工具定义
            tools = kwargs.get("tools", [])

            # 构建请求参数
            request_params = {
                "model": self.model,
                "messages": messages,
                "stream": True,
                **self._get_generation_params(**kwargs)
            }

            # 只有在有工具时才添加tools参数
            if tools:
                request_params["tools"] = tools

            stream = await self.client.chat.completions.create(**request_params)

            async for chunk in stream:
                if hasattr(chunk, 'choices') and chunk.choices:
                    choice = chunk.choices[0]

                    # 处理文本内容
                    if hasattr(choice, 'delta') and choice.delta:
                        if hasattr(choice.delta, 'content') and choice.delta.content:
                            yield LLMResponse(
                                type="text",
                                content=choice.delta.content
                            )

                        # 处理工具调用
                        if hasattr(choice.delta, 'tool_calls') and choice.delta.tool_calls:
                            for tool_call in choice.delta.tool_calls:
                                if hasattr(tool_call, 'function'):
                                    yield LLMResponse(
                                        type="tool_call",
                                        tool_call={
                                            "name": tool_call.function.name if tool_call.function.name else "",
                                            "args": tool_call.function.arguments if tool_call.function.arguments else "{}",
                                            "call_id": tool_call.id if hasattr(tool_call, 'id') else ""
                                        }
                                    )

        except Exception as e:
            logger.error(f"Qwen generation error: {e}")
            yield LLMResponse(type="error", content=str(e))
    
    def _get_generation_params(self, **kwargs) -> Dict[str, Any]:
        """获取生成参数"""
        return {
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 4000),
            "top_p": kwargs.get("top_p", 0.9),
        }