"""读取多个文件工具"""
import os
import glob
from typing import List, Dict, Any
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.file_utils import is_text_file

class ReadManyFilesTool(BaseTool):
    """读取多个文件的工具"""
    
    name = "read_many_files"
    description = "Read multiple files from the filesystem using glob patterns"
    
    parameters = {
        "type": "object",
        "properties": {
            "paths": {
                "type": "array",
                "items": {"type": "string"},
                "description": "List of file paths or glob patterns to read"
            },
            "max_files": {
                "type": "integer",
                "default": 50,
                "description": "Maximum number of files to read"
            }
        },
        "required": ["paths"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行文件读取"""
        paths = args.get("paths", [])
        max_files = args.get("max_files", 50)
        
        if not paths:
            return ToolResult(
                success=False,
                content="No paths provided",
                display="Error: No paths provided"
            )
        
        try:
            all_files = []
            for pattern in paths:
                if os.path.isfile(pattern):
                    all_files.append(pattern)
                else:
                    # 使用 glob 模式匹配
                    matched_files = glob.glob(pattern, recursive=True)
                    all_files.extend(matched_files)
            
            # 限制文件数量
            all_files = all_files[:max_files]
            
            # 过滤出文本文件
            text_files = [f for f in all_files if is_text_file(f)]
            
            # 读取文件内容
            file_contents = []
            for file_path in text_files:
                try:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        content = await f.read()
                        file_contents.append(f"--- {file_path} ---\n{content}")
                except Exception as e:
                    file_contents.append(f"--- {file_path} ---\nError reading file: {e}")
            
            result_content = "\n\n".join(file_contents)
            
            return ToolResult(
                success=True,
                content=result_content,
                display=f"Read {len(text_files)} files successfully"
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Error reading files: {e}",
                display=f"Error: {e}"
            )
