"""文件内容搜索工具"""
import os
import re
import glob
from typing import Dict, Any, List, Tuple
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class GrepTool(BaseTool):
    """文件内容搜索工具"""
    
    name = "grep"
    description = "Search for patterns in file contents using regular expressions"
    
    parameters = {
        "type": "object",
        "properties": {
            "pattern": {
                "type": "string",
                "description": "Regular expression pattern to search for"
            },
            "path": {
                "type": "string",
                "description": "Directory or file path to search in (default: current directory)",
                "default": "."
            },
            "file_pattern": {
                "type": "string",
                "description": "File pattern to include (e.g., '*.py', '*.{js,ts}')",
                "default": "*"
            },
            "case_sensitive": {
                "type": "boolean",
                "description": "Whether search should be case sensitive",
                "default": False
            },
            "max_results": {
                "type": "integer",
                "description": "Maximum number of results to return",
                "default": 100
            },
            "context_lines": {
                "type": "integer",
                "description": "Number of context lines to show around matches",
                "default": 2
            }
        },
        "required": ["pattern"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行文件内容搜索"""
        try:
            pattern = args["pattern"]
            search_path = args.get("path", ".")
            file_pattern = args.get("file_pattern", "*")
            case_sensitive = args.get("case_sensitive", False)
            max_results = args.get("max_results", 100)
            context_lines = args.get("context_lines", 2)
            
            # 检查路径是否存在
            if not os.path.exists(search_path):
                return ToolResult(
                    success=False,
                    content=f"Path does not exist: {search_path}",
                    display=f"❌ Path not found: {search_path}"
                )
            
            # 编译正则表达式
            try:
                flags = 0 if case_sensitive else re.IGNORECASE
                regex = re.compile(pattern, flags)
            except re.error as e:
                return ToolResult(
                    success=False,
                    content=f"Invalid regex pattern: {pattern}. Error: {str(e)}",
                    display=f"❌ Invalid regex pattern: {str(e)}"
                )
            
            # 查找匹配的文件
            files_to_search = self._find_files(search_path, file_pattern)
            
            if not files_to_search:
                return ToolResult(
                    success=True,
                    content=f"No files found matching pattern '{file_pattern}' in {search_path}",
                    display=f"📁 No files found matching '{file_pattern}'"
                )
            
            # 搜索匹配内容
            matches = await self._search_files(files_to_search, regex, context_lines, max_results)
            
            if not matches:
                return ToolResult(
                    success=True,
                    content=f"No matches found for pattern '{pattern}'",
                    display=f"🔍 No matches found for '{pattern}'"
                )
            
            # 格式化结果
            result_content = self._format_results(matches, pattern)
            
            match_count = len(matches)
            file_count = len(set(match[0] for match in matches))
            
            display_info = f"🔍 Found {match_count} matches in {file_count} files\n"
            display_info += f"Pattern: {pattern}\n"
            display_info += f"Search path: {search_path}\n"
            display_info += f"File pattern: {file_pattern}\n\n"
            display_info += result_content
            
            return ToolResult(
                success=True,
                content=result_content,
                display=display_info
            )
            
        except Exception as e:
            logger.error(f"Error in grep: {e}")
            return ToolResult(
                success=False,
                content=f"Error searching files: {str(e)}",
                display=f"❌ Error searching files: {str(e)}"
            )
    
    def _find_files(self, search_path: str, file_pattern: str) -> List[str]:
        """查找匹配的文件"""
        files = []
        
        if os.path.isfile(search_path):
            return [search_path]
        
        # 处理文件模式
        if file_pattern == "*":
            # 搜索所有文本文件
            patterns = ["*.txt", "*.py", "*.js", "*.ts", "*.java", "*.cpp", "*.c", "*.h", 
                       "*.css", "*.html", "*.xml", "*.json", "*.yaml", "*.yml", "*.md", 
                       "*.rst", "*.sh", "*.bat", "*.ps1", "*.sql", "*.php", "*.rb", "*.go"]
        else:
            patterns = [file_pattern]
        
        for pattern in patterns:
            if os.path.isdir(search_path):
                # 递归搜索
                search_pattern = os.path.join(search_path, "**", pattern)
                files.extend(glob.glob(search_pattern, recursive=True))
            else:
                files.extend(glob.glob(pattern))
        
        # 过滤掉目录和二进制文件
        text_files = []
        for file_path in files:
            if os.path.isfile(file_path) and self._is_text_file(file_path):
                text_files.append(file_path)
        
        return text_files
    
    def _is_text_file(self, file_path: str) -> bool:
        """检查是否是文本文件"""
        try:
            # 检查文件扩展名
            text_extensions = {
                '.txt', '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.css', 
                '.html', '.xml', '.json', '.yaml', '.yml', '.md', '.rst', '.sh', 
                '.bat', '.ps1', '.sql', '.php', '.rb', '.go', '.rs', '.kt', '.swift'
            }
            
            _, ext = os.path.splitext(file_path.lower())
            if ext in text_extensions:
                return True
            
            # 尝试读取文件开头判断是否是文本
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                if b'\0' in chunk:  # 包含空字节，可能是二进制文件
                    return False
                
                # 尝试解码为UTF-8
                try:
                    chunk.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    return False
                    
        except Exception:
            return False
    
    async def _search_files(self, files: List[str], regex: re.Pattern, context_lines: int, max_results: int) -> List[Tuple[str, int, str, List[str]]]:
        """在文件中搜索匹配内容"""
        matches = []
        
        for file_path in files:
            if len(matches) >= max_results:
                break
                
            try:
                async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = await f.read()
                    lines = content.split('\n')
                    
                    for line_num, line in enumerate(lines, 1):
                        if regex.search(line):
                            # 获取上下文行
                            start_line = max(0, line_num - 1 - context_lines)
                            end_line = min(len(lines), line_num + context_lines)
                            context = lines[start_line:end_line]
                            
                            matches.append((file_path, line_num, line, context))
                            
                            if len(matches) >= max_results:
                                break
                                
            except Exception as e:
                logger.warning(f"Error reading file {file_path}: {e}")
                continue
        
        return matches
    
    def _format_results(self, matches: List[Tuple[str, int, str, List[str]]], pattern: str) -> str:
        """格式化搜索结果"""
        if not matches:
            return "No matches found."
        
        result_lines = []
        current_file = None
        
        for file_path, line_num, matched_line, context in matches:
            # 文件分隔符
            if file_path != current_file:
                if current_file is not None:
                    result_lines.append("")  # 空行分隔不同文件
                result_lines.append(f"=== {file_path} ===")
                current_file = file_path
            
            # 显示匹配行和上下文
            result_lines.append(f"Line {line_num}: {matched_line.strip()}")
            
            # 如果有上下文，显示上下文
            if len(context) > 1:
                result_lines.append("Context:")
                for i, ctx_line in enumerate(context):
                    ctx_line_num = line_num - len(context)//2 + i
                    if ctx_line_num == line_num:
                        result_lines.append(f"  > {ctx_line_num}: {ctx_line.strip()}")
                    else:
                        result_lines.append(f"    {ctx_line_num}: {ctx_line.strip()}")
        
        return "\n".join(result_lines)
