"""Qwen Code 交互式控制台"""
import asyncio
from typing import Optional
from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.markdown import Markdown
from rich.prompt import Prompt

from ..agent.qwen_agent import QwenAgent
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)

class QwenConsole:
    """Qwen Code 交互式控制台"""
    
    def __init__(self, agent: QwenAgent, config: Config):
        self.agent = agent
        self.config = config
        self.console = Console()
        self.running = True
        
    async def run(self):
        """运行交互式会话"""
        self.console.print("\n[bold blue]Qwen Code is ready![/bold blue]")
        self.console.print("Type your message or '/help' for commands.\n")
        
        while self.running:
            try:
                # 获取用户输入
                user_input = await self._get_user_input()
                
                if not user_input.strip():
                    continue
                
                # 处理命令
                if user_input.startswith('/'):
                    await self._handle_command(user_input)
                    continue
                
                # 处理普通消息
                await self._handle_message(user_input)
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use /exit to quit properly.[/yellow]")
            except Exception as e:
                logger.error(f"Console error: {e}")
                self.console.print(f"[red]Error: {e}[/red]")
    
    async def _get_user_input(self) -> str:
        """获取用户输入"""
        return await asyncio.get_event_loop().run_in_executor(
            None, 
            lambda: Prompt.ask("[bold green]You[/bold green]")
        )
    
    async def _handle_command(self, command: str):
        """处理命令"""
        cmd = command.lower().strip()
        
        if cmd == '/exit' or cmd == '/quit':
            self.console.print("[yellow]Goodbye![/yellow]")
            self.running = False
        elif cmd == '/help':
            self._show_help()
        elif cmd == '/clear':
            self.console.clear()
        elif cmd == '/status':
            await self._show_status()
        else:
            self.console.print(f"[red]Unknown command: {command}[/red]")
    
    async def _handle_message(self, message: str):
        """处理用户消息"""
        self.console.print(f"\n[bold blue]Qwen[/bold blue]: ", end="")
        
        response_text = ""
        
        # 使用 Live 显示流式响应
        with Live("", refresh_per_second=10) as live:
            async for event in self.agent.send_message(message):
                event_type = event.get("type")
                event_data = event.get("data")

                if event_type == "text":
                    response_text += event_data
                    live.update(Markdown(response_text))
                elif event_type == "tool_result":
                    tool_info = event_data
                    if tool_info.get("success"):
                        self.console.print(f"\n[dim]✓ Used tool: {tool_info.get('tool_name')}[/dim]")
                        if tool_info.get("display"):
                            self.console.print(f"[dim]{tool_info.get('display')}[/dim]")
                    else:
                        self.console.print(f"\n[red]✗ Tool error: {tool_info.get('error')}[/red]")
                elif event_type == "error":
                    self.console.print(f"\n[red]Error: {event_data}[/red]")
        
        self.console.print("\n")
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
# Qwen Code Commands

**Basic Commands:**
- `/help` - Show this help message
- `/exit` or `/quit` - Exit the application
- `/clear` - Clear the console
- `/status` - Show current status

**File References:**
- `@filename` - Reference a specific file in your message
- `@folder/` - Reference all files in a folder

**Examples:**
- "Explain the main function in @main.py"
- "Refactor the code in @src/ to improve performance"
- "Create a new test file for @utils.py"
        """
        
        self.console.print(Panel(Markdown(help_text), title="Help", border_style="blue"))
    
    async def _show_status(self):
        """显示当前状态"""
        status_info = f"""
# Current Status

**Configuration:**
- LLM Provider: {self.config.llm_provider}
- Model: {self.config.qwen_model}
- Working Directory: {self.config.working_dir}

**Memory:**
- Memory files loaded: {len(self.agent.memory_manager.memory_content)}
- Chat history length: {len(self.agent.memory_manager.chat_history)}
        """
        
        self.console.print(Panel(Markdown(status_info), title="Status", border_style="green"))