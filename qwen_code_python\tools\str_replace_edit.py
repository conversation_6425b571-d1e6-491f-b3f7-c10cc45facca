"""字符串替换编辑工具"""
import os
from typing import Dict, Any
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class StrReplaceEditTool(BaseTool):
    """基于字符串替换的文件编辑工具"""
    
    name = "str_replace_edit"
    description = "Edit files by replacing exact string matches"
    
    parameters = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "enum": ["str_replace", "create", "view"],
                "description": "The editing command to execute"
            },
            "path": {
                "type": "string",
                "description": "Path to the file to edit"
            },
            "old_str": {
                "type": "string",
                "description": "The exact string to replace (for str_replace command)"
            },
            "new_str": {
                "type": "string",
                "description": "The replacement string (for str_replace command)"
            },
            "file_text": {
                "type": "string",
                "description": "The full content for new file (for create command)"
            }
        },
        "required": ["command", "path"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行编辑操作"""
        command = args.get("command")
        path = args.get("path")
        
        if not path:
            return ToolResult(
                success=False,
                content="No file path provided",
                display="Error: No file path provided"
            )
        
        try:
            if command == "view":
                return await self._view_file(path)
            elif command == "create":
                return await self._create_file(path, args.get("file_text", ""))
            elif command == "str_replace":
                return await self._str_replace(path, args.get("old_str", ""), args.get("new_str", ""))
            else:
                return ToolResult(
                    success=False,
                    content=f"Unknown command: {command}",
                    display=f"Error: Unknown command {command}"
                )
                
        except Exception as e:
            logger.error(f"Edit tool error: {e}")
            return ToolResult(
                success=False,
                content=f"Error: {e}",
                display=f"Error: {e}"
            )
    
    async def _view_file(self, path: str) -> ToolResult:
        """查看文件内容"""
        if not os.path.exists(path):
            return ToolResult(
                success=False,
                content=f"File not found: {path}",
                display=f"Error: File not found: {path}"
            )
        
        async with aiofiles.open(path, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        return ToolResult(
            success=True,
            content=content,
            display=f"Viewed file: {path}"
        )
    
    async def _create_file(self, path: str, content: str) -> ToolResult:
        """创建新文件"""
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        async with aiofiles.open(path, 'w', encoding='utf-8') as f:
            await f.write(content)
        
        return ToolResult(
            success=True,
            content=f"Created file: {path}",
            display=f"Created file: {path} ({len(content)} characters)"
        )
    
    async def _str_replace(self, path: str, old_str: str, new_str: str) -> ToolResult:
        """执行字符串替换"""
        if not os.path.exists(path):
            return ToolResult(
                success=False,
                content=f"File not found: {path}",
                display=f"Error: File not found: {path}"
            )
        
        # 读取原文件
        async with aiofiles.open(path, 'r', encoding='utf-8') as f:
            original_content = await f.read()
        
        # 检查是否存在要替换的字符串
        if old_str not in original_content:
            return ToolResult(
                success=False,
                content=f"String not found in file: {old_str}",
                display=f"Error: String not found in {path}"
            )
        
        # 执行替换
        new_content = original_content.replace(old_str, new_str)
        
        # 写回文件
        async with aiofiles.open(path, 'w', encoding='utf-8') as f:
            await f.write(new_content)
        
        return ToolResult(
            success=True,
            content=f"Successfully replaced string in {path}",
            display=f"Edited {path}: replaced 1 occurrence"
        )