// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

#include "arrow/util/config.h"  // IWYU pragma: export

#include "arrow/filesystem/filesystem.h"  // IWYU pragma: export
#ifdef ARROW_AZURE
#  include "arrow/filesystem/azurefs.h"  // IWYU pragma: export
#endif
#ifdef ARROW_GCS
#  include "arrow/filesystem/gcsfs.h"  // IWYU pragma: export
#endif
#include "arrow/filesystem/hdfs.h"     // IWYU pragma: export
#include "arrow/filesystem/localfs.h"  // IWYU pragma: export
#include "arrow/filesystem/mockfs.h"   // IWYU pragma: export
#ifdef ARROW_S3
#  include "arrow/filesystem/s3fs.h"  // IWYU pragma: export
#endif
