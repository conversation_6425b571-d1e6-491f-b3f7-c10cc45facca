"""文件操作工具"""
import os
import mimetypes
from typing import List
from pathlib import Path

def find_qwen_files(directory: str) -> List[str]:
    """查找 QWEN.md 文件"""
    qwen_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file == "QWEN.md":
                qwen_files.append(os.path.join(root, file))
    
    return qwen_files

def is_text_file(file_path: str) -> bool:
    """判断是否为文本文件"""
    try:
        # 检查文件扩展名
        text_extensions = {
            '.txt', '.md', '.py', '.js', '.ts', '.html', '.css', '.json',
            '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
            '.c', '.cpp', '.h', '.hpp', '.java', '.go', '.rs', '.php',
            '.rb', '.pl', '.r', '.sql', '.log', '.csv', '.tsv'
        }
        
        ext = Path(file_path).suffix.lower()
        if ext in text_extensions:
            return True
        
        # 使用 mimetypes 检查
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type and mime_type.startswith('text/'):
            return True
        
        # 尝试读取文件开头判断
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                # 检查是否包含空字节（二进制文件的特征）
                if b'\x00' in chunk:
                    return False
                # 尝试解码为 UTF-8
                chunk.decode('utf-8')
                return True
        except (UnicodeDecodeError, IOError):
            return False
            
    except Exception:
        return False

def get_folder_structure(directory: str, max_depth: int = 3) -> str:
    """获取文件夹结构"""
    def _build_tree(path: str, prefix: str = "", depth: int = 0) -> List[str]:
        if depth > max_depth:
            return []
        
        items = []
        try:
            entries = sorted(os.listdir(path))
            for i, entry in enumerate(entries):
                if entry.startswith('.'):
                    continue
                    
                entry_path = os.path.join(path, entry)
                is_last = i == len(entries) - 1
                
                current_prefix = "└── " if is_last else "├── "
                items.append(f"{prefix}{current_prefix}{entry}")
                
                if os.path.isdir(entry_path) and depth < max_depth:
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    items.extend(_build_tree(entry_path, next_prefix, depth + 1))
                    
        except PermissionError:
            pass
            
        return items
    
    tree_lines = [os.path.basename(directory) + "/"]
    tree_lines.extend(_build_tree(directory))
    return "\n".join(tree_lines)