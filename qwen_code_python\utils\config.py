"""配置管理"""
import json
import os
from typing import List, Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class Config:
    """配置类"""
    qwen_model: str = "qwen-coder-plus"
    llm_provider: str = "qwen"  # qwen, openai
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    enabled_tools: List[str] = field(default_factory=lambda: [
        "read_many_files", "edit_file", "shell_command", "save_memory",
        "read_file", "write_file", "ls", "grep"
    ])
    max_tokens: int = 4000
    temperature: float = 0.1
    memory_dir: str = ".qwen"
    working_dir: str = "."
    max_chat_history_length: int = 100
    debug: bool = False

    # API配置的便捷属性
    @property
    def qwen_api_key(self) -> str:
        return self.api_key if self.llm_provider == "qwen" else ""

    @property
    def qwen_base_url(self) -> str:
        return self.base_url if self.llm_provider == "qwen" else ""

    @property
    def openai_api_key(self) -> str:
        return self.api_key if self.llm_provider == "openai" else ""

    @property
    def openai_base_url(self) -> str:
        return self.base_url if self.llm_provider == "openai" else ""
    
    def __post_init__(self):
        """初始化后处理"""
        # 从环境变量获取 API Key
        if not self.api_key:
            if self.llm_provider == "qwen":
                self.api_key = os.getenv("QWEN_API_KEY", "")
                if not self.base_url:
                    self.base_url = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
            elif self.llm_provider == "openai":
                self.api_key = os.getenv("OPENAI_API_KEY", "")
                if not self.base_url:
                    self.base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

        # 设置工作目录为当前目录
        if self.working_dir == ".":
            self.working_dir = os.getcwd()

        # 创建内存目录
        if not os.path.exists(self.memory_dir):
            os.makedirs(self.memory_dir, exist_ok=True)

def load_config(config_file: str = "qwen_config.json") -> Config:
    """加载配置文件"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return Config(**config_data)
        except Exception as e:
            print(f"Warning: Failed to load config file {config_file}: {e}")
            return Config()
    else:
        print(f"Config file {config_file} not found, using default config")
        return Config()
