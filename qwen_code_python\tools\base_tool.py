"""工具基类"""
from abc import ABC, abstractmethod
from typing import Dict, Any
from dataclasses import dataclass

from ..utils.config import Config

@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    content: str
    display: str = ""

class BaseTool(ABC):
    """工具基类"""
    
    name: str = ""
    description: str = ""
    parameters: Dict[str, Any] = {}
    
    def __init__(self, config: Config):
        self.config = config
    
    @abstractmethod
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行工具"""
        pass