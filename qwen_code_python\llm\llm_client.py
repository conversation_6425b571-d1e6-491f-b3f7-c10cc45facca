"""LLM 客户端基类"""
from abc import ABC, abstractmethod
from typing import AsyncGenerator, Dict, Any
from dataclasses import dataclass

from ..utils.config import Config

@dataclass
class LLMResponse:
    """LLM 响应基类"""
    type: str  # "text", "tool_call", "error"
    content: str = ""
    tool_call: Dict[str, Any] = None

class LLMClient(ABC):
    """LLM 客户端抽象基类"""
    
    def __init__(self, config: Config):
        self.config = config
    
    @abstractmethod
    async def generate_stream(self, prompt: str, **kwargs) -> AsyncGenerator[LLMResponse, None]:
        """流式生成响应"""
        pass