# Qwen Code Python

Python implementation of Qwen Code - an AI-powered code assistant optimized for Qwen3-Coder models.

## Features

- **Code Understanding & Editing** - Query and edit large codebases beyond traditional context window limits
- **Workflow Automation** - Automate operational tasks like handling pull requests and complex rebases  
- **Enhanced Parser** - Adapted parser specifically optimized for Qwen-Coder models
- **Rich Tool Set** - Comprehensive tools for file operations, shell commands, and memory management

## Quick Start

### Prerequisites

- Python 3.8 or higher
- uv (recommended) or pip for package management

### Installation with uv

```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone the repository
git clone <repository-url>
cd qwen_code_python

# Create virtual environment and install dependencies
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -e .
```

### Installation with pip

```bash
# Clone the repository
git clone <repository-url>
cd qwen_code_python

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -e .
```

### Configuration

1. Initialize configuration:
```bash
qwen init
```

2. Edit the `.env` file and add your API keys:
```bash
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
```

3. Optionally edit `qwen_config.json` to customize settings.

### Usage

Start the interactive session:
```bash
qwen
```

Or use specific commands:
```bash
qwen --help
qwen --model qwen-coder-plus
qwen --provider qwen
```

## Available Tools

- **edit_file** - Edit files by replacing old string with new string
- **read_file** - Read and return file contents with optional line ranges
- **write_file** - Write content to files with directory creation
- **shell_command** - Execute shell commands safely
- **ls** - List directory contents with detailed information
- **grep** - Search for patterns in file contents using regex
- **save_memory** - Save important information for future reference
- **read_many_files** - Read multiple files or directories at once

## Configuration

The configuration file `qwen_config.json` supports:

```json
{
  "qwen_model": "qwen-coder-plus",
  "llm_provider": "qwen",
  "api_key": "",
  "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "enabled_tools": [
    "read_many_files",
    "edit_file", 
    "shell_command",
    "save_memory",
    "read_file",
    "write_file",
    "ls",
    "grep"
  ],
  "max_tokens": 4000,
  "temperature": 0.1,
  "memory_dir": ".qwen",
  "debug": false
}
```

## Development

### Setup Development Environment

```bash
# Install development dependencies
uv pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black qwen_code_python/
isort qwen_code_python/
```

### Type Checking

```bash
mypy qwen_code_python/
```

## Architecture

The project follows a modular architecture:

- **agent/** - Core agent logic and tool execution
- **llm/** - LLM client implementations (Qwen, OpenAI)
- **tools/** - Individual tool implementations
- **memory/** - Memory management system
- **cli/** - Command-line interface and console
- **utils/** - Utility functions and configuration

## License

Apache License 2.0

## Contributing

Contributions are welcome! Please read the contributing guidelines and submit pull requests.

## Comparison with Original

This Python implementation aims to provide feature parity with the original TypeScript/Node.js version while offering:

- Native Python ecosystem integration
- Simplified deployment and packaging
- Enhanced tool extensibility
- Better async/await support
