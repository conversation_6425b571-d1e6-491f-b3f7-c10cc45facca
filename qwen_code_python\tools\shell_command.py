"""Shell命令执行工具"""
import asyncio
import os
import platform
from typing import Dict, Any, Optional
import subprocess

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ShellCommandTool(BaseTool):
    """Shell命令执行工具"""
    
    name = "shell_command"
    description = "Execute shell commands and return the output. Use with caution."
    
    parameters = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "description": "The shell command to execute"
            },
            "working_dir": {
                "type": "string",
                "description": "Working directory for the command (optional, defaults to current directory)"
            },
            "timeout": {
                "type": "integer",
                "description": "Timeout in seconds (default: 30)",
                "default": 30
            }
        },
        "required": ["command"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行shell命令"""
        try:
            command = args["command"]
            working_dir = args.get("working_dir", ".")
            timeout = args.get("timeout", 30)
            
            # 安全检查 - 禁止危险命令
            dangerous_commands = [
                "rm -rf", "del /f", "format", "fdisk", 
                "mkfs", "dd if=", "shutdown", "reboot",
                "halt", "poweroff", "init 0", "init 6"
            ]
            
            command_lower = command.lower()
            for dangerous in dangerous_commands:
                if dangerous in command_lower:
                    return ToolResult(
                        success=False,
                        content=f"Dangerous command blocked: {command}",
                        display=f"❌ Dangerous command blocked for safety"
                    )
            
            # 确保工作目录存在
            if not os.path.exists(working_dir):
                return ToolResult(
                    success=False,
                    content=f"Working directory does not exist: {working_dir}",
                    display=f"❌ Working directory not found: {working_dir}"
                )
            
            # 根据操作系统选择shell
            if platform.system() == "Windows":
                shell_cmd = ["cmd", "/c", command]
            else:
                shell_cmd = ["bash", "-c", command]
            
            # 执行命令
            start_time = asyncio.get_event_loop().time()
            
            process = await asyncio.create_subprocess_exec(
                *shell_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=working_dir
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return ToolResult(
                    success=False,
                    content=f"Command timed out after {timeout} seconds",
                    display=f"❌ Command timed out after {timeout}s"
                )
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            # 解码输出
            stdout_text = stdout.decode('utf-8', errors='replace') if stdout else ""
            stderr_text = stderr.decode('utf-8', errors='replace') if stderr else ""
            
            # 构建结果
            return_code = process.returncode
            success = return_code == 0
            
            # 构建输出内容
            output_parts = []
            if stdout_text:
                output_parts.append(f"STDOUT:\n{stdout_text}")
            if stderr_text:
                output_parts.append(f"STDERR:\n{stderr_text}")
            
            output_content = "\n\n".join(output_parts) if output_parts else "(No output)"
            
            # 构建显示信息
            status_icon = "✅" if success else "❌"
            display_info = f"{status_icon} Command executed in {execution_time:.2f}s\n"
            display_info += f"Command: {command}\n"
            display_info += f"Working dir: {working_dir}\n"
            display_info += f"Return code: {return_code}\n\n"
            
            # 限制输出长度
            if len(output_content) > 2000:
                display_info += output_content[:2000] + "\n... (output truncated)"
            else:
                display_info += output_content
            
            return ToolResult(
                success=success,
                content=output_content,
                display=display_info
            )
            
        except Exception as e:
            logger.error(f"Error in shell_command: {e}")
            return ToolResult(
                success=False,
                content=f"Error executing command: {str(e)}",
                display=f"❌ Error executing command: {str(e)}"
            )
