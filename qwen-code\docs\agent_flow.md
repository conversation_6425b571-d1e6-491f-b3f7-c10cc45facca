# Qwen-Code Agent 运行流程详解

## 1. 初始化阶段

```
用户启动 CLI → 加载配置 → 创建 QwenAgent 实例
                ↓
        初始化 LLM 客户端 (QwenClient/OpenAIClient)
                ↓
        初始化工具执行器 (ToolExecutor)
                ↓
        初始化内存管理器 (MemoryManager)
```

## 2. 消息处理流程

### 2.1 QwenAgent.send_message()
- 刷新内存管理器状态
- 创建新的 Turn 实例
- 启动 agentic 循环

### 2.2 Turn.run() 核心循环
```python
async def run(self, message: str, context: Optional[Dict] = None):
    # 1. 构建提示词
    full_prompt = await self._build_prompt(message, context)
    
    # 2. 获取工具定义
    tools = self.tool_executor.get_tool_definitions()
    
    # 3. LLM 流式响应处理
    async for response in self.llm_client.generate_stream(full_prompt, tools=tools):
        if response.type == "text":
            yield {"type": "text", "data": response.content}
        elif response.type == "tool_call":
            # 工具调用处理
            tool_result = await self._execute_tool_call(response.tool_call)
            yield {"type": "tool_result", "data": tool_result}
            
            # 继续对话
            if tool_result.get("success"):
                tool_prompt = self._build_tool_result_prompt(tool_result)
                async for follow_up in self.llm_client.generate_stream(tool_prompt):
                    yield {"type": "text", "data": follow_up.content}
```

## 3. 工具执行流程

```
LLM 请求工具调用 → ToolExecutor.execute_tool()
                        ↓
                动态加载工具模块 (importlib)
                        ↓
                执行具体工具 (BaseTool.execute)
                        ↓
                返回 ToolResult (success, content, display)
```

## 4. 内存管理

- **QWEN.md 文件系统**: 每个项目目录的 QWEN.md 文件作为持久化内存
- **聊天历史管理**: 维护对话上下文，支持压缩
- **内存刷新**: 每次对话前刷新内存状态

## 5. 流式输出

```
LLM 响应 → Turn 处理 → AgentEvent 生成 → QwenConsole 显示
    ↓           ↓            ↓              ↓
  文本/工具    事件分发    结构化事件      Rich 渲染
```