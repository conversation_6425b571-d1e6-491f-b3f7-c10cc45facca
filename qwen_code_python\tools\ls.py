"""目录列表工具"""
import os
import stat
from typing import Dict, Any, List
from datetime import datetime

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class LsTool(BaseTool):
    """目录列表工具"""
    
    name = "ls"
    description = "List directory contents with detailed information"
    
    parameters = {
        "type": "object",
        "properties": {
            "path": {
                "type": "string",
                "description": "Path to list (default: current directory)",
                "default": "."
            },
            "show_hidden": {
                "type": "boolean",
                "description": "Show hidden files (starting with .)",
                "default": False
            },
            "recursive": {
                "type": "boolean", 
                "description": "List subdirectories recursively",
                "default": False
            },
            "max_depth": {
                "type": "integer",
                "description": "Maximum recursion depth (default: 2)",
                "default": 2
            }
        },
        "required": []
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行目录列表"""
        try:
            path = args.get("path", ".")
            show_hidden = args.get("show_hidden", False)
            recursive = args.get("recursive", False)
            max_depth = args.get("max_depth", 2)
            
            # 检查路径是否存在
            if not os.path.exists(path):
                return ToolResult(
                    success=False,
                    content=f"Path does not exist: {path}",
                    display=f"❌ Path not found: {path}"
                )
            
            # 如果是文件，显示文件信息
            if os.path.isfile(path):
                file_info = self._get_file_info(path)
                return ToolResult(
                    success=True,
                    content=file_info,
                    display=f"📄 File info: {path}\n{file_info}"
                )
            
            # 列出目录内容
            if recursive:
                content = self._list_recursive(path, show_hidden, max_depth, 0)
            else:
                content = self._list_directory(path, show_hidden)
            
            return ToolResult(
                success=True,
                content=content,
                display=f"📁 Directory listing: {path}\n{content}"
            )
            
        except Exception as e:
            logger.error(f"Error in ls: {e}")
            return ToolResult(
                success=False,
                content=f"Error listing directory: {str(e)}",
                display=f"❌ Error listing directory: {str(e)}"
            )
    
    def _list_directory(self, path: str, show_hidden: bool) -> str:
        """列出单个目录的内容"""
        try:
            entries = os.listdir(path)
            if not show_hidden:
                entries = [e for e in entries if not e.startswith('.')]
            
            if not entries:
                return "(Empty directory)"
            
            # 分类和排序
            dirs = []
            files = []
            
            for entry in entries:
                entry_path = os.path.join(path, entry)
                if os.path.isdir(entry_path):
                    dirs.append(entry)
                else:
                    files.append(entry)
            
            dirs.sort()
            files.sort()
            
            # 构建输出
            lines = []
            
            # 目录
            for dir_name in dirs:
                dir_path = os.path.join(path, dir_name)
                try:
                    item_count = len(os.listdir(dir_path))
                    lines.append(f"📁 {dir_name}/ ({item_count} items)")
                except PermissionError:
                    lines.append(f"📁 {dir_name}/ (permission denied)")
            
            # 文件
            for file_name in files:
                file_path = os.path.join(path, file_name)
                try:
                    size = os.path.getsize(file_path)
                    size_str = self._format_size(size)
                    lines.append(f"📄 {file_name} ({size_str})")
                except (OSError, PermissionError):
                    lines.append(f"📄 {file_name} (error reading)")
            
            return "\n".join(lines)
            
        except PermissionError:
            return "Permission denied"
        except Exception as e:
            return f"Error: {str(e)}"
    
    def _list_recursive(self, path: str, show_hidden: bool, max_depth: int, current_depth: int) -> str:
        """递归列出目录内容"""
        if current_depth >= max_depth:
            return ""
        
        lines = []
        indent = "  " * current_depth
        
        try:
            entries = os.listdir(path)
            if not show_hidden:
                entries = [e for e in entries if not e.startswith('.')]
            
            dirs = []
            files = []
            
            for entry in entries:
                entry_path = os.path.join(path, entry)
                if os.path.isdir(entry_path):
                    dirs.append(entry)
                else:
                    files.append(entry)
            
            dirs.sort()
            files.sort()
            
            # 显示当前目录
            if current_depth == 0:
                lines.append(f"{path}/")
            
            # 显示文件
            for file_name in files:
                file_path = os.path.join(path, file_name)
                try:
                    size = os.path.getsize(file_path)
                    size_str = self._format_size(size)
                    lines.append(f"{indent}📄 {file_name} ({size_str})")
                except (OSError, PermissionError):
                    lines.append(f"{indent}📄 {file_name} (error)")
            
            # 递归显示目录
            for dir_name in dirs:
                dir_path = os.path.join(path, dir_name)
                try:
                    item_count = len(os.listdir(dir_path))
                    lines.append(f"{indent}📁 {dir_name}/ ({item_count} items)")
                    
                    # 递归
                    if current_depth + 1 < max_depth:
                        sub_content = self._list_recursive(dir_path, show_hidden, max_depth, current_depth + 1)
                        if sub_content:
                            lines.append(sub_content)
                            
                except PermissionError:
                    lines.append(f"{indent}📁 {dir_name}/ (permission denied)")
            
            return "\n".join(lines)
            
        except PermissionError:
            return f"{indent}Permission denied"
        except Exception as e:
            return f"{indent}Error: {str(e)}"
    
    def _get_file_info(self, file_path: str) -> str:
        """获取文件详细信息"""
        try:
            stat_info = os.stat(file_path)
            size = stat_info.st_size
            mtime = datetime.fromtimestamp(stat_info.st_mtime)
            
            # 文件类型
            mode = stat_info.st_mode
            if stat.S_ISREG(mode):
                file_type = "Regular file"
            elif stat.S_ISDIR(mode):
                file_type = "Directory"
            elif stat.S_ISLNK(mode):
                file_type = "Symbolic link"
            else:
                file_type = "Special file"
            
            # 权限
            permissions = stat.filemode(mode)
            
            info_lines = [
                f"Type: {file_type}",
                f"Size: {self._format_size(size)}",
                f"Modified: {mtime.strftime('%Y-%m-%d %H:%M:%S')}",
                f"Permissions: {permissions}"
            ]
            
            return "\n".join(info_lines)
            
        except Exception as e:
            return f"Error getting file info: {str(e)}"
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
