"""文件编辑工具 - 基于字符串替换的精确编辑"""
import os
import difflib
from typing import Dict, Any
import aiofiles

from .base_tool import BaseTool, ToolResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

class EditFileTool(BaseTool):
    """文件编辑工具 - 基于字符串替换的精确编辑"""
    
    name = "edit_file"
    description = "Edit files by replacing old string with new string. Supports creating new files when old_string is empty."
    
    parameters = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file to edit (relative or absolute)"
            },
            "old_string": {
                "type": "string", 
                "description": "String to replace. Use empty string to create new file or append to end."
            },
            "new_string": {
                "type": "string",
                "description": "String to replace with"
            }
        },
        "required": ["file_path", "old_string", "new_string"]
    }
    
    async def execute(self, args: Dict[str, Any]) -> ToolResult:
        """执行文件编辑"""
        try:
            file_path = args["file_path"]
            old_string = args["old_string"]
            new_string = args["new_string"]
            
            # 确保目录存在
            if os.path.dirname(file_path):
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 处理新文件创建
            if old_string == "" and not os.path.exists(file_path):
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    await f.write(new_string)
                return ToolResult(
                    success=True,
                    content=f"Created new file: {file_path}",
                    display=f"✅ Created new file: {file_path}"
                )
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    content=f"File not found: {file_path}. Use empty old_string to create new file.",
                    display=f"❌ File not found: {file_path}"
                )
            
            # 读取文件内容
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                original_content = await f.read()
            
            # 处理空字符串替换（追加到文件末尾）
            if old_string == "":
                new_content = original_content + new_string
            else:
                # 检查old_string是否存在
                if old_string not in original_content:
                    # 提供相似内容建议
                    lines = original_content.split('\n')
                    suggestions = []
                    for i, line in enumerate(lines):
                        if any(word in line.lower() for word in old_string.lower().split() if len(word) > 2):
                            suggestions.append(f"Line {i+1}: {line.strip()}")
                    
                    suggestion_text = ""
                    if suggestions:
                        suggestion_text = f"\n\nSimilar content found:\n" + "\n".join(suggestions[:3])
                    
                    return ToolResult(
                        success=False,
                        content=f"String not found in file: {old_string}{suggestion_text}",
                        display=f"❌ String not found in file"
                    )
                
                # 检查是否有多个匹配
                occurrences = original_content.count(old_string)
                if occurrences > 1:
                    return ToolResult(
                        success=False,
                        content=f"Multiple occurrences ({occurrences}) of string found. Please be more specific.",
                        display=f"❌ Multiple occurrences found ({occurrences})"
                    )
                
                # 执行替换
                new_content = original_content.replace(old_string, new_string)
            
            # 写入文件
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(new_content)
            
            # 生成差异显示
            diff = self._generate_diff(original_content, new_content, file_path)
            
            return ToolResult(
                success=True,
                content=f"Successfully edited {file_path}",
                display=f"✅ Successfully edited {file_path}\n\n{diff}"
            )
            
        except Exception as e:
            logger.error(f"Error in edit_file: {e}")
            return ToolResult(
                success=False,
                content=f"Error: {str(e)}",
                display=f"❌ Error: {str(e)}"
            )
    
    def _generate_diff(self, original: str, new: str, filename: str) -> str:
        """生成差异显示"""
        if original == new:
            return "No changes made."
        
        original_lines = original.splitlines(keepends=True)
        new_lines = new.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            new_lines,
            fromfile=f"{filename} (original)",
            tofile=f"{filename} (modified)",
            lineterm=""
        )
        
        diff_text = "".join(diff)
        if len(diff_text) > 1000:
            return f"Large diff ({len(diff_text)} chars) - file successfully modified."
        
        return diff_text if diff_text else "File content updated."
