"""Qwen Code CLI 主入口"""
import asyncio
import typer
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown

from ..agent.qwen_agent import QwenAgent
from ..utils.config import Config, load_config
from ..utils.logger import setup_logger
from .console import QwenConsole

app = typer.Typer(name="qwen", help="Qwen Code - AI-powered code assistant")
console = Console()

@app.command()
def main(
    config_file: str = typer.Option("qwen_config.json", help="Configuration file path"),
    debug: bool = typer.Option(False, help="Enable debug mode"),
    model: str = typer.Option(None, help="Override model name"),
    provider: str = typer.Option(None, help="Override LLM provider")
):
    """启动 Qwen Code 交互式会话"""
    
    # 设置日志
    setup_logger(debug=debug)
    
    try:
        # 加载配置
        config = load_config(config_file)
        if model:
            config.qwen_model = model
        if provider:
            config.llm_provider = provider
        
        # 显示欢迎信息
        welcome_text = """
# Welcome to Qwen Code

Qwen Code is an AI-powered code assistant that helps you understand, edit, and work with codebases.

**Available commands:**
- Type your questions or requests naturally
- Use `@filename` to reference specific files
- Type `/help` for more commands
- Type `/exit` to quit

Let's start coding! 🚀
        """
        
        console.print(Panel(Markdown(welcome_text), title="Qwen Code", border_style="blue"))
        
        # 启动交互式会话
        asyncio.run(start_interactive_session(config))
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        raise typer.Exit(1)

async def start_interactive_session(config: Config):
    """启动交互式会话"""
    try:
        agent = QwenAgent(config)
        qwen_console = QwenConsole(agent, config)
        await qwen_console.run()
    except Exception as e:
        console.print(f"[red]Failed to start session: {e}[/red]")
        raise

@app.command()
def version():
    """显示版本信息"""
    console.print("Qwen Code Python v0.1.0")

@app.command()
def init():
    """初始化配置文件"""
    import json
    import os
    
    config_file = "qwen_config.json"
    env_file = ".env"
    
    # 创建配置文件
    if not os.path.exists(config_file):
        default_config = {
            "qwen_model": "qwen-coder-plus",
            "llm_provider": "qwen",
            "api_key": "",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "enabled_tools": [
                "read_many_files",
                "edit_file", 
                "shell_command",
                "save_memory"
            ],
            "max_tokens": 4000,
            "temperature": 0.1,
            "memory_dir": ".qwen",
            "debug": False
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        console.print(f"[green]Created {config_file}[/green]")
    
    # 创建环境变量文件
    if not os.path.exists(env_file):
        env_content = """# Qwen API 配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# OpenAI API 配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 调试模式
DEBUG=false
"""
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        console.print(f"[green]Created {env_file}[/green]")
    
    console.print("\n[blue]Next steps:[/blue]")
    console.print("1. Edit .env file and add your API keys")
    console.print("2. Run: qwen")

if __name__ == "__main__":
    app()
