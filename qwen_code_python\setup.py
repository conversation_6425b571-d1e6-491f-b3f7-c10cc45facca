#!/usr/bin/env python3
"""
Setup script for Qwen Code Python
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd, check=True):
    """Run a command and return the result"""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return None

def check_python():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_uv():
    """Check if uv is available"""
    result = run_command("uv --version", check=False)
    if result and result.returncode == 0:
        print("✓ uv is available")
        return True
    else:
        print("⚠ uv not found, will use pip instead")
        return False

def setup_with_uv():
    """Setup using uv"""
    print("\n=== Setting up with uv ===")
    
    # Create virtual environment
    run_command("uv venv")
    
    # Install dependencies
    run_command("uv pip install -r requirements.txt")
    
    # Install in development mode
    run_command("uv pip install -e .")
    
    print("✓ Setup complete with uv")

def setup_with_pip():
    """Setup using pip"""
    print("\n=== Setting up with pip ===")
    
    # Create virtual environment
    run_command(f"{sys.executable} -m venv venv")
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix-like
        activate_script = "venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Install dependencies
    run_command(f"{pip_cmd} install -r requirements.txt")
    
    # Install in development mode
    run_command(f"{pip_cmd} install -e .")
    
    print("✓ Setup complete with pip")
    print(f"To activate the environment, run: {activate_script}")

def create_config_files():
    """Create configuration files if they don't exist"""
    print("\n=== Creating configuration files ===")
    
    # Create .env file
    env_file = Path(".env")
    if not env_file.exists():
        env_content = """# Qwen API Configuration
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# OpenAI API Configuration (optional)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Debug mode
DEBUG=false
"""
        env_file.write_text(env_content)
        print("✓ Created .env file")
    else:
        print("✓ .env file already exists")
    
    # Create qwen_config.json
    config_file = Path("qwen_config.json")
    if not config_file.exists():
        config_content = """{
  "qwen_model": "qwen-coder-plus",
  "llm_provider": "qwen",
  "api_key": "",
  "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "enabled_tools": [
    "read_many_files",
    "edit_file",
    "shell_command",
    "save_memory",
    "read_file",
    "write_file",
    "ls",
    "grep"
  ],
  "max_tokens": 4000,
  "temperature": 0.1,
  "memory_dir": ".qwen",
  "debug": false
}"""
        config_file.write_text(config_content)
        print("✓ Created qwen_config.json")
    else:
        print("✓ qwen_config.json already exists")

def main():
    """Main setup function"""
    print("🚀 Qwen Code Python Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python():
        sys.exit(1)
    
    # Check for uv
    has_uv = check_uv()
    
    # Setup environment
    if has_uv:
        setup_with_uv()
    else:
        setup_with_pip()
    
    # Create config files
    create_config_files()
    
    print("\n" + "=" * 40)
    print("🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Optionally edit qwen_config.json to customize settings")
    if has_uv:
        print("3. Run: python run.py")
    else:
        print("3. Activate the virtual environment")
        print("4. Run: python run.py")

if __name__ == "__main__":
    main()
